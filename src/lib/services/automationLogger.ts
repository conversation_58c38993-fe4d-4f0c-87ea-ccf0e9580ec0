import { supabase } from '@/integrations/supabase/client';
import { CampaignStatus } from '@/lib/agents/types';

export interface AutomationLogData {
  id?: string;
  campaign_id: string;
  user_id: string;
  automation_mode: 'AUTOMATIC' | 'MANUAL';
  status: string;
  total_steps?: number;
  completed_steps?: number;
  failed_steps?: number;
  creators_found?: number;
  creators_selected?: number;
  creators_contacted?: number;
  contracts_generated?: number;
  contracts_sent?: number;
  contracts_signed?: number;
  emails_sent?: number;
  emails_failed?: number;
  phone_calls_made?: number;
  phone_calls_failed?: number;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  execution_plan?: any;
  error_details?: any;
  final_report?: any;
}

export interface AutomationStepData {
  automation_log_id: string;
  step_name: string;
  step_type: 'INITIALIZATION' | 'CREATOR_SEARCH' | 'CONTRACT_GENERATION' | 'EMAIL_OUTREACH' | 'PHONE_OUTREACH' | 'RESPONSE_PROCESSING' | 'COMPLETION';
  step_order: number;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
  description?: string;
  input_data?: any;
  output_data?: any;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
}

export interface AutomationCommunicationData {
  automation_log_id: string;
  automation_step_id?: string;
  creator_id?: string;
  communication_type: 'EMAIL' | 'PHONE' | 'SYSTEM';
  status: 'SENT' | 'FAILED' | 'PENDING';
  content: string;
  email_address?: string;
  email_subject?: string;
  email_response?: any;
  phone_number?: string;
  call_duration_seconds?: number;
  call_response?: any;
}

export class AutomationLogger {
  private static readonly DEFAULT_USER_ID = 'e5c58861-fada-4c8c-bbe7-f7aff2879fcb';

  // Create a new automation log
  static async createAutomationLog(data: Partial<AutomationLogData>): Promise<string | null> {
    try {
      const logData: AutomationLogData = {
        campaign_id: data.campaign_id!,
        user_id: data.user_id || this.DEFAULT_USER_ID,
        automation_mode: data.automation_mode || 'AUTOMATIC',
        status: data.status || CampaignStatus.INITIATED,
        total_steps: 0,
        completed_steps: 0,
        failed_steps: 0,
        creators_found: 0,
        creators_selected: 0,
        creators_contacted: 0,
        contracts_generated: 0,
        contracts_sent: 0,
        contracts_signed: 0,
        emails_sent: 0,
        emails_failed: 0,
        phone_calls_made: 0,
        phone_calls_failed: 0,
        started_at: new Date().toISOString(),
        ...data
      };

      const { data: result, error } = await supabase
        .from('automation_logs')
        .insert(logData)
        .select('id')
        .single();

      if (error) {
        console.error('Error creating automation log:', error);
        return null;
      }

      return result?.id || null;
    } catch (error) {
      console.error('Error in createAutomationLog:', error);
      return null;
    }
  }

  // Update automation log
  static async updateAutomationLog(id: string, updates: Partial<AutomationLogData>): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Calculate duration if completed
      if (updates.status === CampaignStatus.COMPLETED || updates.status === CampaignStatus.FAILED) {
        updateData.completed_at = new Date().toISOString();
        
        // Get the started_at time to calculate duration
        const { data: logData } = await supabase
          .from('automation_logs')
          .select('started_at')
          .eq('id', id)
          .single();

        if (logData?.started_at) {
          const startTime = new Date(logData.started_at).getTime();
          const endTime = new Date().getTime();
          updateData.duration_seconds = Math.floor((endTime - startTime) / 1000);
        }
      }

      const { error } = await supabase
        .from('automation_logs')
        .update(updateData)
        .eq('id', id);

      if (error) {
        console.error('Error updating automation log:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateAutomationLog:', error);
      return false;
    }
  }

  // Log a step
  static async logStep(stepData: AutomationStepData): Promise<string | null> {
    try {
      const data = {
        ...stepData,
        started_at: stepData.started_at || new Date().toISOString()
      };

      const { data: result, error } = await supabase
        .from('automation_steps')
        .insert(data)
        .select('id')
        .single();

      if (error) {
        console.error('Error logging step:', error);
        return null;
      }

      // Update total steps count
      await this.incrementCounter(stepData.automation_log_id, 'total_steps');

      return result?.id || null;
    } catch (error) {
      console.error('Error in logStep:', error);
      return null;
    }
  }

  // Update step status
  static async updateStep(stepId: string, updates: Partial<AutomationStepData>): Promise<boolean> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Calculate duration if completed
      if (updates.status === 'COMPLETED' || updates.status === 'FAILED') {
        updateData.completed_at = new Date().toISOString();
        
        // Get the started_at time to calculate duration
        const { data: stepData } = await supabase
          .from('automation_steps')
          .select('started_at, automation_log_id')
          .eq('id', stepId)
          .single();

        if (stepData?.started_at) {
          const startTime = new Date(stepData.started_at).getTime();
          const endTime = new Date().getTime();
          updateData.duration_seconds = Math.floor((endTime - startTime) / 1000);
        }

        // Update counters
        if (updates.status === 'COMPLETED' && stepData?.automation_log_id) {
          await this.incrementCounter(stepData.automation_log_id, 'completed_steps');
        } else if (updates.status === 'FAILED' && stepData?.automation_log_id) {
          await this.incrementCounter(stepData.automation_log_id, 'failed_steps');
        }
      }

      const { error } = await supabase
        .from('automation_steps')
        .update(updateData)
        .eq('id', stepId);

      if (error) {
        console.error('Error updating step:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateStep:', error);
      return false;
    }
  }

  // Log communication
  static async logCommunication(commData: AutomationCommunicationData): Promise<string | null> {
    try {
      const { data: result, error } = await supabase
        .from('automation_communications')
        .insert(commData)
        .select('id')
        .single();

      if (error) {
        console.error('Error logging communication:', error);
        return null;
      }

      // Update communication counters
      if (commData.communication_type === 'EMAIL') {
        const counter = commData.status === 'SENT' ? 'emails_sent' : 'emails_failed';
        await this.incrementCounter(commData.automation_log_id, counter);
      } else if (commData.communication_type === 'PHONE') {
        const counter = commData.status === 'SENT' ? 'phone_calls_made' : 'phone_calls_failed';
        await this.incrementCounter(commData.automation_log_id, counter);
      }

      return result?.id || null;
    } catch (error) {
      console.error('Error in logCommunication:', error);
      return null;
    }
  }

  // Increment counter in automation log
  static async incrementCounter(automationLogId: string, counterName: string, increment: number = 1): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('increment_automation_counter', {
        log_id: automationLogId,
        counter_name: counterName,
        increment_by: increment
      });

      if (error) {
        // Fallback to manual increment if RPC doesn't exist
        const { data: currentData } = await supabase
          .from('automation_logs')
          .select(counterName)
          .eq('id', automationLogId)
          .single();

        if (currentData) {
          const currentValue = currentData[counterName] || 0;
          await supabase
            .from('automation_logs')
            .update({ [counterName]: currentValue + increment })
            .eq('id', automationLogId);
        }
      }

      return true;
    } catch (error) {
      console.error('Error incrementing counter:', error);
      return false;
    }
  }

  // Get automation report
  static async getAutomationReport(automationLogId: string) {
    try {
      const { data: logData, error: logError } = await supabase
        .from('automation_logs')
        .select('*')
        .eq('id', automationLogId)
        .single();

      if (logError) throw logError;

      const { data: stepsData, error: stepsError } = await supabase
        .from('automation_steps')
        .select('*')
        .eq('automation_log_id', automationLogId)
        .order('step_order');

      if (stepsError) throw stepsError;

      const { data: communicationsData, error: commError } = await supabase
        .from('automation_communications')
        .select('*')
        .eq('automation_log_id', automationLogId)
        .order('sent_at');

      if (commError) throw commError;

      return {
        log: logData,
        steps: stepsData || [],
        communications: communicationsData || []
      };
    } catch (error) {
      console.error('Error getting automation report:', error);
      return null;
    }
  }

  // Get all automation logs for a campaign
  static async getCampaignAutomationLogs(campaignId: string) {
    try {
      const { data, error } = await supabase
        .from('automation_logs')
        .select('*')
        .eq('campaign_id', campaignId)
        .order('started_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting campaign automation logs:', error);
      return [];
    }
  }
}
